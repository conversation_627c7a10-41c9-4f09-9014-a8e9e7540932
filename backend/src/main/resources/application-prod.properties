# Medical Home Sampling Application - PRODUCTION Configuration
spring.application.name=medical-home-sampling

# Server Configuration
server.address=0.0.0.0
server.port=8080
server.servlet.context-path=/api

# PostgreSQL Database Configuration (Production - dynamique)
spring.datasource.url=jdbc:postgresql://${DB_HOST:postgres}:5432/${DB_NAME:medical_home_sampling}
spring.datasource.username=${DB_USERNAME:postgres}
spring.datasource.password=${DB_PASSWORD:postgres}
spring.datasource.driver-class-name=org.postgresql.Driver

# Connection pool settings
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=10
spring.datasource.hikari.connection-timeout=20000

# JPA/Hibernate Configuration
spring.jpa.hibernate.ddl-auto=${DDL_AUTO:update}
spring.jpa.show-sql=${SHOW_SQL:false}
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# JWT Configuration
spring.security.jwt.secret=${JWT_SECRET:mySecretKey123456789012345678901234567890}
spring.security.jwt.expiration=${JWT_EXPIRATION:86400000}

# Swagger/OpenAPI Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operations-sorter=method

# Logging Configuration (Production - moins verbeux)
logging.level.com.medical.homesampling=INFO
logging.level.org.springframework.security=WARN
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n
logging.file.name=logs/medical-home-sampling.log

# CORS Configuration
CORS_ORIGINS=https://prev.intellitech.pro,http://localhost:3000,http://localhost:8080

# SQL Initialization
spring.jpa.defer-datasource-initialization=true
spring.sql.init.mode=${INIT_MODE:always}
spring.sql.init.data-locations=classpath:data.sql

# Application configuration
app.name=MediSample
app.mail.from=<EMAIL>

# Email configuration (Production)
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=yfhc uraa ycku fvrv
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true

# En mode production, vraie connexion email (test désactivé pour éviter erreurs au démarrage)
spring.mail.test-connection=false
