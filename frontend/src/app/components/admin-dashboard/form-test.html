<!-- Test simple pour vérifier le formulaire -->
<div style="padding: 20px; max-width: 500px;">
  <h2>Test du formulaire d'ajout d'infirmier</h2>
  
  <mat-form-field appearance="outline" style="width: 100%; margin-bottom: 16px;">
    <mat-label>Test Prénom</mat-label>
    <mat-icon matPrefix>badge</mat-icon>
    <input matInput placeholder="Entrez le prénom" required>
    <mat-error>Ce champ est requis</mat-error>
  </mat-form-field>

  <mat-form-field appearance="outline" style="width: 100%; margin-bottom: 16px;">
    <mat-label>Test Email</mat-label>
    <mat-icon matPrefix>email</mat-icon>
    <input matInput type="email" placeholder="<EMAIL>" required>
    <mat-error>Email invalide</mat-error>
  </mat-form-field>

  <mat-form-field appearance="outline" style="width: 100%; margin-bottom: 16px;">
    <mat-label>Test Mot de passe</mat-label>
    <mat-icon matPrefix>lock</mat-icon>
    <input matInput type="password" placeholder="Minimum 8 caractères" required>
    <mat-hint>Le mot de passe doit contenir au moins 8 caractères.</mat-hint>
    <mat-error>Ce champ est requis</mat-error>
  </mat-form-field>
</div>
